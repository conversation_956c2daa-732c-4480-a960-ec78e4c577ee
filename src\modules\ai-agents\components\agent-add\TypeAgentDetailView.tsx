import {
  But<PERSON>,
  Card,
  Container,
  EmptyState,
  Icon,
  InlineEditInput,
  Loading,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { formatDate } from '@/shared/utils/date';
import React, { useCallback, useState } from 'react';
import { useGetTypeAgentDetail } from '../../hooks/useTypeAgent';
// Removed unused imports
import ConfigurationSection from './ConfigurationSection';
import { TypeAgentDetailDto } from '../../types/dto';

interface TypeAgentDetailViewProps {
  /**
   * ID của type agent cần hiển thị chi tiết
   */
  typeAgentId: number | null;

  /**
   * Trạng thái hiển thị của detail view
   */
  isVisible: boolean;

  /**
   * Callback khi đóng detail view
   */
  onClose: () => void;
}

/**
 * Component hiển thị chi tiết type agent với layout đẹp
 * Sử dụng SlideInForm để hiển thị với animation mượt mà
 */
const TypeAgentDetailView: React.FC<TypeAgentDetailViewProps> = ({
  typeAgentId,
  isVisible,
  onClose,
}) => {
  // const { t } = useTranslation(); // Removed unused variable
  // Removed activeTab state - only show configuration tab
  const [isUpdatingName, setIsUpdatingName] = useState<boolean>(false);

  // Gọi API để lấy chi tiết type agent
  const {
    data: typeAgentDetailResponse,
    isLoading: isLoadingDetail,
    error: detailError,
  } = useGetTypeAgentDetail(typeAgentId || undefined);

  // Dữ liệu type agent detail - convert từ TypeAgentDto sang TypeAgentDetailDto
  const typeAgentDetail: TypeAgentDetailDto | undefined = typeAgentDetailResponse?.result ? {
    id: typeAgentDetailResponse.result.id,
    name: typeAgentDetailResponse.result.name,
    description: typeAgentDetailResponse.result.description || null,
    config: typeAgentDetailResponse.result.config,
    createdAt: typeAgentDetailResponse.result.createdAt,
    updatedAt: typeAgentDetailResponse.result.updatedAt,
    tools: [], // Default empty array since TypeAgentDto doesn't have tools
    countTool: 0 // Default value since TypeAgentDto doesn't have countTool
  } : undefined;

  // Xử lý khi đóng detail view
  const handleClose = () => {
    onClose();
  };

  // Xử lý update tên type agent
  const handleUpdateName = useCallback(async (newName: string) => {
    if (!typeAgentDetail || !newName.trim()) return;

    setIsUpdatingName(true);

    try {
      // TODO: Implement API call khi có endpoint
      // await updateTypeAgent(typeAgentDetail.id, { name: newName.trim() });

      // Mock success - trong thực tế sẽ refetch data
      console.log('Updating type agent name:', {
        id: typeAgentDetail.id,
        oldName: typeAgentDetail.name,
        newName: newName.trim()
      });

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // TODO: Invalidate queries để refetch data
      // queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.TYPE_AGENT_DETAIL, typeAgentId] });

    } catch (error) {
      console.error('Error updating type agent name:', error);
      throw error; // InlineEditInput sẽ xử lý error
    } finally {
      setIsUpdatingName(false);
    }
  }, [typeAgentDetail]);

  // Validation cho tên type agent
  const validateName = useCallback((name: string) => {
    if (!name.trim()) {
      return 'Tên không được để trống';
    }
    if (name.trim().length < 2) {
      return 'Tên phải có ít nhất 2 ký tự';
    }
    if (name.trim().length > 100) {
      return 'Tên không được vượt quá 100 ký tự';
    }
    return null;
  }, []);

  // Render loading state
  if (isLoadingDetail) {
    return (
      <SlideInForm isVisible={isVisible}>
        <Card className="w-full max-w-4xl mx-auto">
          <div className="flex justify-center items-center py-12">
            <Loading size="lg" />
          </div>
        </Card>
      </SlideInForm>
    );
  }

  // Render error state
  if (detailError || !typeAgentDetail) {
    return (
      <SlideInForm isVisible={isVisible}>
        <Card className="w-full max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <Typography variant="h4">Chi tiết Type Agent</Typography>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              leftIcon={<Icon name="x" size="sm" />}
            >
              Đóng
            </Button>
          </div>

          <EmptyState
            icon="alert-circle"
            title="Không thể tải dữ liệu"
            description="Có lỗi xảy ra khi tải chi tiết type agent. Vui lòng thử lại."
            actions={
              <Button variant="outline" onClick={handleClose}>
                Đóng
              </Button>
            }
          />
        </Card>
      </SlideInForm>
    );
  }

  return (
    <SlideInForm isVisible={isVisible}>
      <Container className="w-full mx-auto">
        {/* Header */}
        <div className="flex justify-between items-start mb-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <InlineEditInput
                value={typeAgentDetail.name}
                onSave={handleUpdateName}
                placeholder="Nhập tên type agent..."
                variant="h4"
                className="text-gray-900 dark:text-gray-100"
                maxLength={100}
                validate={validateName}
                isLoading={isUpdatingName}
              />
            </div>

            {typeAgentDetail.description && (
              <Typography variant="body1" className="text-gray-600 dark:text-gray-400 mb-3">
                {typeAgentDetail.description}
              </Typography>
            )}

            <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <Icon name="calendar" size="sm" />
                <span>Tạo: {formatDate(Number(typeAgentDetail.createdAt))}</span>
              </div>
              <div className="flex items-center gap-1">
                <Icon name="edit" size="sm" />
                <span>Cập nhật: {formatDate(Number(typeAgentDetail.updatedAt))}</span>
              </div>
            </div>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            leftIcon={<Icon name="x" size="sm" />}
          >
            Đóng
          </Button>
        </div>

        {/* Configuration Section - Always visible */}
        <div className="mb-6">
          <Typography variant="h5" className="mb-4">Cấu hình</Typography>
          <ConfigurationSection
            config={typeAgentDetail.config}
            groupTools={typeAgentDetail.tools || []}
          />
        </div>
      </Container>
    </SlideInForm>
  );
};

export default TypeAgentDetailView;
