import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import { getAgentDetail } from '../api/agent.api';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { AgentDetailDto } from '../types';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';

/**
 * Hook để lấy thông tin chi tiết của Agent từ API thực
 * @param id ID của Agent
 * @param options TanStack Query options
 * @returns Query result
 */
export const useAgentDetail = (
  id: string | undefined,
  options?: UseQueryOptions<ApiResponse<AgentDetailDto>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, id],
    queryFn: () => getAgentDetail(id as string),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để lấy data từ agent detail response
 * @param id ID của Agent
 * @param options TanStack Query options
 * @returns Agent detail data
 */
export const useAgentDetailData = (
  id: string | undefined,
  options?: UseQueryOptions<ApiResponse<AgentDetailDto>>
) => {
  const query = useAgentDetail(id, options);

  return {
    ...query,
    data: query.data?.result, // Extract result from API response
  };
};
