import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';

// Facebook Page imports
import {
  AgentFacebookPageDto,
  ConnectFacebookPageDto,
  connectFacebookPageToAgent,
  updateAgentFacebookPage,
  UpdateAgentFacebookPageDto
} from '../api/facebook-page.api';

// Website imports
import {
  AgentWebsiteDto,
  ConnectWebsiteDto,
  connectWebsiteToAgent,
  updateAgentWebsite,
  UpdateAgentWebsiteDto
} from '../api/website.api';

/**
 * Service layer cho Integration - chứa business logic cho Facebook và Website
 */

// ==================== FACEBOOK PAGE SERVICES ====================

/**
 * Kết nối Facebook Page với business logic
 * @param agentId ID của agent
 * @param data Dữ liệu kết nối
 * @returns Promise với response từ API
 */
export const connectFacebookPageWithBusinessLogic = async (
  agentId: string,
  data: ConnectFacebookPageDto
): Promise<ApiResponse<AgentFacebookPageDto>> => {
  // Business logic có thể bao gồm:
  // - Validate Facebook Page ID
  // - Check permissions
  // - Validate settings

  // Validate Facebook Page ID
  if (!data.facebookPageId || typeof data.facebookPageId !== 'string') {
    throw new Error('Facebook Page ID is required');
  }

  // Validate settings if provided
  if (data.settings) {
    if (data.settings.replyDelay !== undefined) {
      if (data.settings.replyDelay < 0 || data.settings.replyDelay > 300) {
        throw new Error('Reply delay must be between 0 and 300 seconds');
      }
    }

    if (data.settings.workingHours) {
      const { start, end, timezone } = data.settings.workingHours;

      // Validate time format (HH:MM)
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (!timeRegex.test(start) || !timeRegex.test(end)) {
        throw new Error('Working hours must be in HH:MM format');
      }

      // Validate timezone
      if (!timezone || timezone.length === 0) {
        throw new Error('Timezone is required for working hours');
      }
    }
  }

  return connectFacebookPageToAgent(agentId, data);
};

/**
 * Cập nhật Facebook Page với business logic
 * @param agentId ID của agent
 * @param pageId ID của Facebook Page
 * @param data Dữ liệu cập nhật
 * @returns Promise với response từ API
 */
export const updateAgentFacebookPageWithBusinessLogic = async (
  agentId: string,
  pageId: string,
  data: UpdateAgentFacebookPageDto
): Promise<ApiResponse<AgentFacebookPageDto>> => {
  // Validate settings if provided
  if (data.settings) {
    if (data.settings.replyDelay !== undefined) {
      if (data.settings.replyDelay < 0 || data.settings.replyDelay > 300) {
        throw new Error('Reply delay must be between 0 and 300 seconds');
      }
    }

    if (data.settings.workingHours) {
      const { start, end, timezone } = data.settings.workingHours;

      // Validate time format (HH:MM)
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (!timeRegex.test(start) || !timeRegex.test(end)) {
        throw new Error('Working hours must be in HH:MM format');
      }

      // Validate timezone
      if (!timezone || timezone.length === 0) {
        throw new Error('Timezone is required for working hours');
      }
    }
  }

  return updateAgentFacebookPage(agentId, pageId, data);
};

// ==================== WEBSITE SERVICES ====================

/**
 * Kết nối Website với business logic
 * @param agentId ID của agent
 * @param data Dữ liệu kết nối
 * @returns Promise với response từ API
 */
export const connectWebsiteWithBusinessLogic = async (
  agentId: string,
  data: ConnectWebsiteDto
): Promise<ApiResponse<AgentWebsiteDto>> => {
  // Business logic có thể bao gồm:
  // - Validate Website ID
  // - Check permissions
  // - Validate settings

  // Validate Website ID
  if (!data.websiteId || typeof data.websiteId !== 'string') {
    throw new Error('Website ID is required');
  }

  // Validate settings if provided
  if (data.settings) {
    // Validate position
    if (data.settings.position) {
      const allowedPositions = ['bottom-right', 'bottom-left', 'top-right', 'top-left'];
      if (!allowedPositions.includes(data.settings.position)) {
        throw new Error('Invalid position. Must be one of: ' + allowedPositions.join(', '));
      }
    }

    // Validate theme
    if (data.settings.theme) {
      const allowedThemes = ['light', 'dark', 'auto'];
      if (!allowedThemes.includes(data.settings.theme)) {
        throw new Error('Invalid theme. Must be one of: ' + allowedThemes.join(', '));
      }
    }

    // Validate primary color (hex format)
    if (data.settings.primaryColor) {
      const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
      if (!hexColorRegex.test(data.settings.primaryColor)) {
        throw new Error('Primary color must be a valid hex color (e.g., #FF0000)');
      }
    }

    // Validate message lengths
    if (data.settings.welcomeMessage && data.settings.welcomeMessage.length > 200) {
      throw new Error('Welcome message must be less than 200 characters');
    }

    if (data.settings.placeholder && data.settings.placeholder.length > 100) {
      throw new Error('Placeholder must be less than 100 characters');
    }
  }

  return connectWebsiteToAgent(agentId, data);
};

/**
 * Cập nhật Website với business logic
 * @param agentId ID của agent
 * @param websiteId ID của Website
 * @param data Dữ liệu cập nhật
 * @returns Promise với response từ API
 */
export const updateAgentWebsiteWithBusinessLogic = async (
  agentId: string,
  websiteId: string,
  data: UpdateAgentWebsiteDto
): Promise<ApiResponse<AgentWebsiteDto>> => {
  // Validate settings if provided
  if (data.settings) {
    // Validate position
    if (data.settings.position) {
      const allowedPositions = ['bottom-right', 'bottom-left', 'top-right', 'top-left'];
      if (!allowedPositions.includes(data.settings.position)) {
        throw new Error('Invalid position. Must be one of: ' + allowedPositions.join(', '));
      }
    }

    // Validate theme
    if (data.settings.theme) {
      const allowedThemes = ['light', 'dark', 'auto'];
      if (!allowedThemes.includes(data.settings.theme)) {
        throw new Error('Invalid theme. Must be one of: ' + allowedThemes.join(', '));
      }
    }

    // Validate primary color (hex format)
    if (data.settings.primaryColor) {
      const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
      if (!hexColorRegex.test(data.settings.primaryColor)) {
        throw new Error('Primary color must be a valid hex color (e.g., #FF0000)');
      }
    }

    // Validate message lengths
    if (data.settings.welcomeMessage && data.settings.welcomeMessage.length > 200) {
      throw new Error('Welcome message must be less than 200 characters');
    }

    if (data.settings.placeholder && data.settings.placeholder.length > 100) {
      throw new Error('Placeholder must be less than 100 characters');
    }
  }

  return updateAgentWebsite(agentId, websiteId, data);
};
