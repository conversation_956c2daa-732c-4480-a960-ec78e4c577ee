import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON> } from '@/shared/components/common';
import { SortDirection } from '../types';

/**
 * Demo component để test sort functionality
 */
const SortDemo: React.FC = () => {
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  const handleSortChange = (newSortBy: string, newSortDirection: SortDirection) => {
    setSortBy(newSortBy);
    setSortDirection(newSortDirection);
  };

  const sortOptions = [
    {
      id: 'createdAt-desc',
      label: '<PERSON><PERSON><PERSON> nhất trước',
      sortBy: 'createdAt',
      direction: SortDirection.DESC,
    },
    {
      id: 'createdAt-asc',
      label: '<PERSON><PERSON> nhất trước',
      sortBy: 'createdAt',
      direction: SortDirection.ASC,
    },
    {
      id: 'name-asc',
      label: 'Tên A-Z',
      sortBy: 'name',
      direction: SortDirection.ASC,
    },
    {
      id: 'name-desc',
      label: 'Tên Z-A',
      sortBy: 'name',
      direction: SortDirection.DESC,
    },
  ];

  return (
    <div className="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          Sort Filter Demo
        </h1>
        
        <Card className="p-6 mb-6">
          <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
            Current Sort Settings
          </h2>
          
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Sort By:
              </label>
              <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                {sortBy}
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Direction:
              </label>
              <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                {sortDirection}
              </div>
            </div>
          </div>
          
          <div className="border-t pt-4">
            <h3 className="text-md font-medium mb-3 text-gray-900 dark:text-white">
              Sort Options:
            </h3>
            
            <div className="grid grid-cols-2 gap-3">
              {sortOptions.map((option) => (
                <Button
                  key={option.id}
                  variant={
                    sortBy === option.sortBy && sortDirection === option.direction
                      ? 'primary'
                      : 'secondary'
                  }
                  onClick={() => handleSortChange(option.sortBy, option.direction)}
                  className="justify-start"
                >
                  {option.label}
                  {sortBy === option.sortBy && sortDirection === option.direction && (
                    <span className="ml-2">✓</span>
                  )}
                </Button>
              ))}
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
            API Query Parameters
          </h2>
          
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <pre className="text-sm text-gray-800 dark:text-gray-200">
{`{
  "page": 1,
  "limit": 12,
  "search": "",
  "sortBy": "${sortBy}",
  "sortDirection": "${sortDirection}",
  "active": undefined
}`}
            </pre>
          </div>
          
          <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
            <p>
              <strong>Mô tả:</strong> Các tham số này sẽ được gửi đến API để lấy danh sách agents 
              với thứ tự sắp xếp theo lựa chọn của user.
            </p>
            
            <ul className="mt-2 space-y-1">
              <li>• <strong>createdAt DESC:</strong> Agents mới tạo sẽ hiển thị trước</li>
              <li>• <strong>createdAt ASC:</strong> Agents cũ sẽ hiển thị trước</li>
              <li>• <strong>name ASC:</strong> Sắp xếp tên từ A đến Z</li>
              <li>• <strong>name DESC:</strong> Sắp xếp tên từ Z đến A</li>
            </ul>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default SortDemo;
