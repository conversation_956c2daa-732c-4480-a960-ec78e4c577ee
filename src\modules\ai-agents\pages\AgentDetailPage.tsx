import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button, Icon, Loading, EmptyState } from '@/shared/components/common';
import { useAgentDetailData } from '../hooks/useAgentDetail';
import { AgentProfile } from '../types/agent.types';
import {
  ProfileSection,
  FacebookIntegrationSection,
  WebsiteIntegrationSection,
  StrategySection,
  ConversionSection,
  VectorSection,
  ResourceSection,
  ModuleSection,
} from '../components/agent-detail';

/**
 * Trang chi tiết Agent
 */
const AgentDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  // Cài đặt trạng thái mở/đóng của mỗi phần
  const [, setExpandedSections] = useState<Record<string, boolean>>({
    profile: true,
    facebook: false,
    website: false,
    conversion: false,
    vector: false,
    strategy: false,
    resource: false,
    module: false,
  });

  // Sử dụng hooks
  const { data: agent, isLoading, error } = useAgentDetailData(id);
  // Note: useUpdateAgent không có trong API mới, sẽ sử dụng specific update hooks
  const isUpdating = false; // Placeholder

  // Xử lý quay lại
  const handleBack = () => {
    navigate('/ai-agents');
  };

  // Xử lý đóng/mở section
  const handleSectionToggle = (section: string, isOpen: boolean) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: isOpen,
    }));
  };

  // Xử lý cập nhật profile
  const handleUpdateProfile = (profile: Partial<AgentProfile>) => {
    // Note: Sẽ sử dụng useUpdateProfile hook từ hooks/useProfile.ts
    console.log('Update profile:', profile);
    // TODO: Implement với useUpdateProfile hook
  };

  // Hiển thị loading
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading size="lg" />
      </div>
    );
  }

  // Hiển thị lỗi
  if (error || !agent) {
    return (
                  <EmptyState        icon="alert-triangle"        title={t('common.error', 'Lỗi')}        description={t('aiAgents.detail.notFound', 'Không tìm thấy Agent hoặc có lỗi xảy ra.')}        actions={          <Button variant="primary" onClick={handleBack}>            {t('common.backToList', 'Quay lại danh sách')}          </Button>        }      />
    );
  }

  return (
    <div className="">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div className="flex items-center mb-4 sm:mb-0">
          <Button
            variant="ghost"
            onClick={handleBack}
            className="mr-4"
            leftIcon={<Icon name="chevron-left" size="sm" />}
          >
            {t('common.back', 'Quay lại')}
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold">{agent.profile.name}</h1>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            leftIcon={<Icon name="refresh-cw" size="sm" />}
            onClick={() => window.location.reload()}
          >
            {t('common.refresh', 'Làm mới')}
          </Button>
          <Button
            variant="secondary"
            leftIcon={<Icon name="edit" size="sm" />}
            onClick={() => navigate(`/ai-agents/${id}/edit`)}
          >
            Chỉnh sửa
          </Button>
          <Button
            variant="primary"
            leftIcon={<Icon name="save" size="sm" />}
            isLoading={isUpdating}
          >
            {t('common.saveAll', 'Lưu tất cả')}
          </Button>
        </div>
      </div>

      {/* Thông tin profile */}
      <ProfileSection
        profile={agent.profile}
        onUpdate={handleUpdateProfile}
        isLoading={isUpdating}
        onToggle={isOpen => handleSectionToggle('profile', isOpen)}
      />

      {/* Tích hợp Facebook */}
      <FacebookIntegrationSection
        agentId={id || ''}
        facebookPages={agent.facebookPages}
        onToggle={isOpen => handleSectionToggle('facebook', isOpen)}
      />

      {/* Tích hợp Website */}
      <WebsiteIntegrationSection
        agentId={id || ''}
        websites={agent.websites}
        onToggle={isOpen => handleSectionToggle('website', isOpen)}
      />

      {/* Chiến lược */}
      <StrategySection
        agentId={id || ''}
        strategy={agent.strategy}
        onToggle={isOpen => handleSectionToggle('strategy', isOpen)}
      />

      {/* Chuyển đổi */}
      <ConversionSection
        agentId={id || ''}
        conversionFields={agent.conversionFields}
        onToggle={isOpen => handleSectionToggle('conversion', isOpen)}
      />

      {/* Vector */}
      <VectorSection
        agentId={id || ''}
        vector={agent.vector}
        onToggle={isOpen => handleSectionToggle('vector', isOpen)}
      />

      {/* Tài nguyên */}
      <ResourceSection
        agentId={id || ''}
        resources={agent.resources}
        onToggle={isOpen => handleSectionToggle('resource', isOpen)}
      />

      {/* Module */}
      <ModuleSection
        agentId={id || ''}
        modules={agent.modules}
        onToggle={isOpen => handleSectionToggle('module', isOpen)}
      />
    </div>
  );
};

export default AgentDetailPage;
