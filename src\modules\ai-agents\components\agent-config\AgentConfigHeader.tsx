/**
 * Agent Configuration Left Panel Component
 * Vertical layout: Avatar + Name + Model Config in left panel
 */

import React from 'react';
import { Icon, InlineEditInput, Chip } from '@/shared/components/common';
import { ModelConfig } from './index';
import { ModelConfigData } from '../../types';

interface AgentConfigLeftPanelProps {
  agentData: {
    avatar?: string;
    name: string;
  };
  typeAgentName?: string; // Tên của TypeAgent được chọn
  onAvatarUpload: () => void;
  onAvatarFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onNameUpdate: (name: string) => void;
  onModelConfigUpdate: (config: ModelConfigData) => void;
  validateAgentName: (name: string) => string | null;
  fileInputRef: React.RefObject<HTMLInputElement>;
  // Action buttons
  onSave?: () => void;
  onCancel?: () => void;
  onDelete?: () => void; // Thêm callback xóa
  isSaving?: boolean;
  hasChanges?: boolean;
  mode?: 'create' | 'edit'; // Thêm mode để hiển thị nút Delete
}

/**
 * Avatar Section Component - Centered for Left Panel
 */
const AvatarSection: React.FC<{
  avatar?: string;
  onUpload: () => void;
  onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
}> = ({ avatar, onUpload, onFileChange, fileInputRef }) => (
  <div className="flex flex-col items-center mb-6">
    <div className="relative w-32 h-32 rounded-full overflow-hidden bg-gradient-to-br from-red-500 to-orange-300 flex items-center justify-center border-4 border-white shadow-lg group cursor-pointer">
      {avatar ? (
        <img
          src={avatar}
          alt="Avatar"
          className="w-full h-full object-cover"
        />
      ) : (
        <div className="text-white">
          <Icon name="user" size="xl" />
        </div>
      )}

      <div
        className="absolute bottom-0 left-0 right-0 h-1/3 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center"
        onClick={onUpload}
      >
        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <Icon name="upload" size="sm" className="text-white" />
        </div>
      </div>
    </div>

    {/* Hidden file input */}
    <input
      ref={fileInputRef}
      type="file"
      accept="image/*"
      onChange={onFileChange}
      className="hidden"
    />
  </div>
);

/**
 * Name and Info Section Component - Centered for Left Panel
 */
const NameInfoSection: React.FC<{
  name: string;
  typeAgentName?: string;
  onNameUpdate: (name: string) => void;
  validateAgentName: (name: string) => string | null;
}> = ({ name, typeAgentName, onNameUpdate, validateAgentName }) => (
  <div className="flex flex-col items-center space-y-4 mb-6">
    {/* Agent Name - Centered */}
    <div className="w-full flex justify-center">
      <InlineEditInput
        value={name}
        onSave={onNameUpdate}
        placeholder="Nhập tên agent"
        className="font-semibold"
        maxLength={100}
        validate={validateAgentName}
        variant="h2"
        centerAligned={true}
        noUnderline={true}
        dynamicWidth={true}
        maxWidth="400px"
        minWidth="120px"
      />
    </div>

    {/* Agent Type Chip - Centered */}
    <div className="flex justify-center">
      <Chip
        variant="primary"
        size="md"
        leftIconName="bot"
        className="bg-gradient-to-r from-red-500  to-yellow-400 text-white shadow-md"
      >
        {typeAgentName || 'AI Agent'}
      </Chip>
    </div>
  </div>
);

/**
 * Model Config Section Component - Full Width for Left Panel
 */
const ModelConfigSection: React.FC<{
  onModelConfigUpdate: (config: ModelConfigData) => void;
}> = ({ onModelConfigUpdate }) => (
  <div className="w-full">
    <ModelConfig
      onSave={onModelConfigUpdate}
    />
  </div>
);

/**
 * Main Left Panel Component
 */
export const AgentConfigLeftPanel: React.FC<AgentConfigLeftPanelProps> = ({
  agentData,
  typeAgentName,
  onAvatarUpload,
  onAvatarFileChange,
  onNameUpdate,
  onModelConfigUpdate,
  validateAgentName,
  fileInputRef,
  onSave,
  onCancel,
  onDelete,
  isSaving = false,
  hasChanges = false,
  mode = 'create'
}) => {
  return (
    <div className="w-full h-full flex flex-col p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg sticky top-4 overflow-hidden min-h-[600px]">
      {/* Content */}
      <div className="flex flex-col h-full">
        {/* Avatar Section - Centered */}
        <AvatarSection
          avatar={agentData.avatar}
          onUpload={onAvatarUpload}
          onFileChange={onAvatarFileChange}
          fileInputRef={fileInputRef}
        />

        {/* Name Section - Centered */}
        <NameInfoSection
          name={agentData.name}
          typeAgentName={typeAgentName}
          onNameUpdate={onNameUpdate}
          validateAgentName={validateAgentName}
        />

        {/* Model Config Section - Full Width */}
        <ModelConfigSection
          onModelConfigUpdate={onModelConfigUpdate}
        />

        {/* Action Buttons */}
        {(onSave || onCancel || onDelete) && (
          <div className="space-y-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
            {/* Primary Actions Row */}
            <div className="flex gap-3">
              {onCancel && (
                <button
                  type="button"
                  onClick={onCancel}
                  disabled={isSaving}
                  className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Hủy
                </button>
              )}
              {onSave && (
                <button
                  type="button"
                  onClick={onSave}
                  disabled={isSaving || !hasChanges}
                  className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isSaving ? (
                    <div className="flex items-center justify-center gap-2">
                      <Icon name="loader-2" size="sm" className="animate-spin" />
                      <span>Đang lưu...</span>
                    </div>
                  ) : (
                    'Lưu'
                  )}
                </button>
              )}
            </div>

            {/* Delete Button - Only in Edit Mode */}
            {onDelete && mode === 'edit' && (
              <button
                type="button"
                onClick={onDelete}
                disabled={isSaving}
                className="w-full px-4 py-2 text-sm font-medium text-red-600 dark:text-red-400 bg-white dark:bg-gray-700 border border-red-300 dark:border-red-600 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <div className="flex items-center justify-center gap-2">
                  <Icon name="trash-2" size="sm" />
                  <span>Xóa Agent</span>
                </div>
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Export với tên cũ để backward compatibility
export const AgentConfigHeader = AgentConfigLeftPanel;
