import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import {
  getSimpleAgents,
  getAgents,
  getAgentDetail,
  createAgent,
  updateAgent,
  deleteAgent,
  toggleAgentActive,
  getAgentStatistics,
  updateAgentVectorStore,
  AgentSimpleListDto,
  AgentSimpleQueryDto,
  AgentListItemDto
} from '../api/agent.api';

import {
  GetAgentsQueryDto,
  CreateAgentDto,
  UpdateAgentDto,
  AgentStatisticsQueryDto,
  UpdateAgentVectorStoreDto,
  AgentDetailDto,
  AgentStatisticsResponseDto,
} from '../types';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';



/**
 * Hook để lấy danh sách agent đ<PERSON><PERSON> giản (chỉ id, avatar, name)
 */
export const useGetSimpleAgents = (
  params?: AgentSimpleQueryDto,
  options?: UseQueryOptions<ApiResponse<PaginatedResult<AgentSimpleListDto>>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.SIMPLE_AGENT_LIST, params],
    queryFn: () => getSimpleAgents(params),
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook để lấy danh sách agents đầy đủ
 */
export const useGetAgents = (
  params?: GetAgentsQueryDto,
  options?: UseQueryOptions<ApiResponse<PaginatedResult<AgentListItemDto>>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.AGENT_LIST, params],
    queryFn: () => getAgents(params),
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook để lấy chi tiết agent
 * @param id ID của agent
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetAgentDetail = (
  id: string | undefined,
  options?: UseQueryOptions<ApiResponse<AgentDetailDto>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, id],
    queryFn: () => getAgentDetail(id as string),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để lấy thống kê agent
 * @param id ID của agent
 * @param params Query params
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetAgentStatistics = (
  id: string | undefined,
  params?: AgentStatisticsQueryDto,
  options?: UseQueryOptions<ApiResponse<AgentStatisticsResponseDto>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.AGENT_STATISTICS, id, params],
    queryFn: () => getAgentStatistics(id as string, params),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  });
};

/**
 * Hook để tạo agent mới
 * @returns Mutation result
 */
export const useCreateAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.CREATE_AGENT],
    mutationFn: (data: CreateAgentDto) => createAgent(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách agents
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_LIST] });
    },
  });
};

/**
 * Hook để cập nhật agent
 * @returns Mutation result
 */
export const useUpdateAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.UPDATE_AGENT],
    mutationFn: ({ id, data }: { id: string; data: UpdateAgentDto }) =>
      updateAgent(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate chi tiết agent và danh sách
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, id] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_LIST] });
    },
  });
};

/**
 * Hook để xóa agent
 * @returns Mutation result
 */
export const useDeleteAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.DELETE_AGENT],
    mutationFn: (id: string) => deleteAgent(id),
    onSuccess: (_, id) => {
      // Remove agent khỏi cache và invalidate danh sách
      queryClient.removeQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, id] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_LIST] });
    },
  });
};

/**
 * Hook để bật/tắt agent - approach đơn giản với cache update
 * @returns Mutation result
 */
export const useToggleAgentActive = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.TOGGLE_AGENT_ACTIVE],
    mutationFn: (id: string) => toggleAgentActive(id),

    // Cập nhật cache sau khi API thành công
    onSuccess: (_, id) => {
      // Cập nhật agent list cache
      queryClient.setQueryData([AGENT_QUERY_KEYS.AGENT_LIST], (old: any) => {
        if (!old?.result?.items) return old;

        return {
          ...old,
          result: {
            ...old.result,
            items: old.result.items.map((agent: AgentListItemDto) =>
              agent.id === id ? { ...agent, active: !agent.active } : agent
            ),
          },
        };
      });

      // Cập nhật agent detail cache nếu có
      queryClient.setQueryData([AGENT_QUERY_KEYS.AGENT_DETAIL, id], (old: any) => {
        if (!old?.result) return old;

        return {
          ...old,
          result: {
            ...old.result,
            active: !old.result.active,
          },
        };
      });
    },
  });
};

/**
 * Hook để cập nhật vector store của agent
 * @returns Mutation result
 */
export const useUpdateAgentVectorStore = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.UPDATE_AGENT_VECTOR_STORE],
    mutationFn: ({ id, data }: { id: string; data: UpdateAgentVectorStoreDto }) =>
      updateAgentVectorStore(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate chi tiết agent
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, id] });
    },
  });
};
